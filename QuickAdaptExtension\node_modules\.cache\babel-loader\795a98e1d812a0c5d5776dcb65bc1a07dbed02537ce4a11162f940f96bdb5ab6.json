{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\RTEsection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, forwardRef, useMemo } from \"react\";\nimport { Box, Tooltip, IconButton } from \"@mui/material\";\nimport JoditEditor from \"jodit-react\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RTEsection = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  textBoxRef,\n  isBanner,\n  handleDeleteRTESection,\n  index,\n  guidePopUpRef,\n  onClone,\n  isCloneDisabled\n}, ref) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    rtesContainer,\n    updateRTEContainer,\n    setIsUnSavedChanges,\n    cloneRTEContainer,\n    clearRteDetails,\n    selectedTemplate,\n    selectedTemplateTour,\n    announcementGuideMetaData,\n    toolTipGuideMetaData,\n    handleAnnouncementRTEValue,\n    handleTooltipRTEValue,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementRTEContainer\n  } = useDrawerStore();\n\n  // Individual state management for each RTE\n  const [editingRTEId, setEditingRTEId] = useState(null);\n  const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState(null);\n  const [hoveredRTEId, setHoveredRTEId] = useState(null);\n  const contentRef = useRef(\"\");\n  const [contentStates, setContentStates] = useState(new Map());\n\n  // Map to store individual refs for each RTE\n  const editorRefs = useRef(new Map());\n  const containerRefs = useRef(new Map());\n\n  // Helper function to get or create editor ref for specific RTE\n  const getEditorRef = rteId => {\n    if (!editorRefs.current.has(rteId)) {\n      editorRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return editorRefs.current.get(rteId);\n  };\n\n  // Helper function to get or create container ref for specific RTE\n  const getContainerRef = rteId => {\n    if (!containerRefs.current.has(rteId)) {\n      containerRefs.current.set(rteId, /*#__PURE__*/React.createRef());\n    }\n    return containerRefs.current.get(rteId);\n  };\n\n  // Handle clicks outside the editor\n  useEffect(() => {\n    const handleClickOutside = event => {\n      var _document$querySelect, _document$querySelect2, _document$querySelect3, _document$querySelect4;\n      if (!editingRTEId) return;\n      const isInsideJoditPopupContent = event.target.closest(\".jodit-popup__content\") !== null;\n      const isInsideAltTextPopup = event.target.closest(\".jodit-ui-input\") !== null;\n      const isInsidePopup = (_document$querySelect = document.querySelector(\".jodit-popup\")) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.contains(event.target);\n      const isInsideJoditPopup = (_document$querySelect2 = document.querySelector(\".jodit-wysiwyg\")) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.contains(event.target);\n      const isInsideWorkplacePopup = isInsideJoditPopup || ((_document$querySelect3 = document.querySelector(\".jodit-dialog__panel\")) === null || _document$querySelect3 === void 0 ? void 0 : _document$querySelect3.contains(event.target));\n      const isSelectionMarker = event.target.id.startsWith(\"jodit-selection_marker_\");\n      const isLinkPopup = (_document$querySelect4 = document.querySelector(\".jodit-ui-input__input\")) === null || _document$querySelect4 === void 0 ? void 0 : _document$querySelect4.contains(event.target);\n      const isInsideToolbarButton = event.target.closest(\".jodit-toolbar-button__button\") !== null;\n      const isInsertButton = event.target.closest(\"button[aria-pressed='false']\") !== null;\n      const currentContainerRef = getContainerRef(editingRTEId);\n      if (currentContainerRef !== null && currentContainerRef !== void 0 && currentContainerRef.current && !currentContainerRef.current.contains(event.target) && !isInsidePopup && !isInsideJoditPopup && !isInsideWorkplacePopup && !isSelectionMarker && !isLinkPopup && !isInsideToolbarButton && !isInsertButton && !isInsideJoditPopupContent && !isInsideAltTextPopup) {\n        setEditingRTEId(null);\n        setToolbarVisibleRTEId(null);\n      }\n    };\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    return () => document.removeEventListener(\"mousedown\", handleClickOutside);\n  }, [editingRTEId]);\n  useEffect(() => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        setTimeout(() => {\n          const joditInstance = editorRef.current.editor;\n          if (joditInstance) {\n            // Focus the editor\n            joditInstance.focus();\n\n            // Move cursor to end of content instead of beginning\n            const range = joditInstance.selection.createRange();\n            range.selectNodeContents(joditInstance.editor);\n            range.collapse(false); // false = collapse to end, true = collapse to start\n            joditInstance.selection.selectRange(range);\n          }\n        }, 100); // Increased timeout for better reliability\n      }\n    }\n  }, [editingRTEId]);\n\n  // Helper function to check if content is empty\n  const isContentEmpty = content => {\n    if (!content) return true;\n    const textContent = content.replace(/<[^>]*>/g, '').trim();\n    return textContent.length === 0;\n  };\n\n  // Helper function to check if content is scrollable\n  const isContentScrollable = containerId => {\n    const containerRef = getContainerRef(containerId);\n    if (containerRef !== null && containerRef !== void 0 && containerRef.current) {\n      const workplace = containerRef.current.querySelector('.jodit-workplace');\n      if (workplace) {\n        return workplace.scrollHeight > workplace.clientHeight;\n      }\n    }\n    return false;\n  };\n\n  // Update content state for dynamic icon positioning\n  const updateContentState = (containerId, content) => {\n    const isEmpty = isContentEmpty(content);\n    const isScrollable = isContentScrollable(containerId);\n    setContentStates(prev => {\n      const newMap = new Map(prev);\n      newMap.set(containerId, {\n        isEmpty,\n        isScrollable\n      });\n      return newMap;\n    });\n  };\n  const handleUpdate = (newContent, rteId, containerId) => {\n    contentRef.current = newContent;\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    const isAITour = createWithAI && selectedTemplate === \"Tour\";\n    const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n    const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n    const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n    if (isAIAnnouncement) {\n      const currentStepIndex = currentStep - 1;\n      if (isTourAnnouncement) {\n        var _toolTipGuideMetaData, _toolTipGuideMetaData2;\n        const tooltipContainer = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : (_toolTipGuideMetaData2 = _toolTipGuideMetaData.containers) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.find(container => container.id === containerId && container.type === \"rte\");\n        if (tooltipContainer) {\n          handleTooltipRTEValue(containerId, newContent);\n        }\n      } else {\n        var _announcementGuideMet, _announcementGuideMet2;\n        const announcementContainer = (_announcementGuideMet = announcementGuideMetaData[currentStepIndex]) === null || _announcementGuideMet === void 0 ? void 0 : (_announcementGuideMet2 = _announcementGuideMet.containers) === null || _announcementGuideMet2 === void 0 ? void 0 : _announcementGuideMet2.find(container => container.id === containerId && container.type === \"rte\");\n        if (announcementContainer) {\n          handleAnnouncementRTEValue(containerId, newContent);\n        }\n      }\n    } else if (isAITour && (isTourBanner || isTourTooltip)) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4;\n      const currentStepIndex = currentStep - 1;\n      const tooltipContainer = (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData3 === void 0 ? void 0 : (_toolTipGuideMetaData4 = _toolTipGuideMetaData3.containers) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.find(container => container.id === containerId && container.type === \"rte\");\n      if (tooltipContainer) {\n        handleTooltipRTEValue(containerId, newContent);\n      }\n    } else {\n      updateRTEContainer(containerId, rteId, newContent);\n    }\n    setIsUnSavedChanges(true);\n    updateContentState(containerId, newContent);\n  };\n  const handleCloneContainer = containerId => {\n    if (isCloneDisabled) {\n      return;\n    }\n    cloneRTEContainer(containerId);\n    if (onClone) {\n      onClone();\n    }\n  };\n  const handleDeleteSection = (containerId, rteId) => {\n    const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n    if (isAIAnnouncement) {\n      clearRteDetails(containerId, rteId);\n    } else {\n      clearRteDetails(containerId, rteId);\n    }\n    handleDeleteRTESection(index);\n  };\n  const handlePaste = event => {\n    event.preventDefault();\n    const clipboardData = event.clipboardData;\n    const pastedText = clipboardData.getData(\"text/plain\");\n    const pastedHtml = clipboardData.getData(\"text/html\");\n    if (pastedHtml) {\n      const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\n      if (isRTEContent) {\n        insertContent(pastedHtml);\n      } else {\n        insertContent(pastedHtml);\n      }\n    } else {\n      insertContent(pastedText);\n    }\n  };\n  const insertContent = content => {\n    if (editingRTEId) {\n      const editorRef = getEditorRef(editingRTEId);\n      if (editorRef !== null && editorRef !== void 0 && editorRef.current) {\n        const editor = editorRef.current.editor;\n        editor.selection.insertHTML(content);\n      }\n    }\n  };\n  const toggleToolbar = rteId => {\n    if (toolbarVisibleRTEId === rteId) {\n      setToolbarVisibleRTEId(null);\n    } else {\n      setToolbarVisibleRTEId(rteId);\n    }\n  };\n  const [isRtlDirection, setIsRtlDirection] = useState(false);\n  useEffect(() => {\n    const dir = document.body.getAttribute(\"dir\") || \"ltr\";\n    setIsRtlDirection(dir.toLowerCase() === \"rtl\");\n  }, []);\n\n  // FIXED JODIT CONFIG - Addresses cursor position and Enter key issues\n  const config = useMemo(() => ({\n    readonly: false,\n    direction: isRtlDirection ? 'rtl' : 'ltr',\n    language: 'en',\n    toolbarSticky: false,\n    toolbarAdaptive: false,\n    toolbar: toolbarVisibleRTEId !== null,\n    // Enhanced paragraph and line behavior\n    enter: 'P',\n    // Creates <p> tags on Enter\n    enterBlock: 'p',\n    // Block element to create on Enter\n    defaultMode: 1,\n    // WYSIWYG mode\n\n    // Ensure proper paragraph behavior\n    cleanHTML: {\n      fillEmptyParagraph: true,\n      // Fill empty paragraphs with &nbsp;\n      replaceNBSP: false,\n      // Keep non-breaking spaces\n      allowTags: true\n    },\n    // Critical cursor positioning fixes\n    autofocus: false,\n    // Disable autofocus to prevent cursor jumping\n    cursorAfterAutofocus: 'end',\n    // When focused, cursor goes to end\n\n    // Line break behavior\n    useBRInsteadOfP: false,\n    // Use <p> tags instead of <br>\n\n    // Prevent plugins that might interfere\n    disablePlugins: ['preview'],\n    // Disable potentially problematic plugins\n\n    // Keyboard shortcuts\n    commandToHotkeys: {\n      'insertLineBreak': 'shift+enter',\n      // Shift+Enter for <br>\n      'insertParagraph': 'enter' // Enter for new paragraph\n    },\n    buttons: ['bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush', 'font', 'fontsize', 'link', {\n      name: 'more',\n      iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\n      list: ['source', 'image', 'video', 'table', 'align', 'undo', 'redo', '|', 'hr', 'eraser', 'copyformat', 'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|', 'outdent', 'indent', 'paragraph']\n    }],\n    popupRoot: document.body,\n    zIndex: 100000,\n    globalFullSize: false,\n    // Fix selection and cursor behavior\n    selection: {\n      mode: 'single'\n    },\n    link: {\n      followOnDblClick: false,\n      processVideoLink: true,\n      processPastedLink: true,\n      openInNewTabCheckbox: true,\n      noFollowCheckbox: false,\n      modeClassName: 'input'\n    },\n    dialog: {\n      zIndex: 100001\n    },\n    // CRITICAL: Enhanced event handling\n    events: {\n      // Custom paste handler\n      onPaste: handlePaste,\n      // Fix cursor positioning after initialization\n      afterInit: jodit => {\n        // Don't auto-focus immediately\n        setTimeout(() => {\n          const editor = jodit.editor;\n          if (editor) {\n            // Set cursor to end of content\n            const range = jodit.selection.createRange();\n            range.selectNodeContents(editor);\n            range.collapse(false); // false = collapse to end\n            jodit.selection.selectRange(range);\n          }\n        }, 100);\n      },\n      // Handle focus events to maintain cursor position\n      focus: jodit => {\n        // Maintain cursor position on focus\n        setTimeout(() => {\n          const selection = jodit.selection;\n          if (selection && !selection.isCollapsed()) {\n            // If there's no selection, move to end of content\n            const range = selection.createRange();\n            range.selectNodeContents(jodit.editor);\n            range.collapse(false);\n            selection.selectRange(range);\n          }\n        }, 0);\n      },\n      // Ensure Enter key works properly\n      keydown: (event, jodit) => {\n        if (event.key === 'Enter' && !event.shiftKey) {\n          // Normal Enter - create new paragraph and ensure cursor moves\n          // Let Jodit handle this naturally, it will create <p> and move cursor\n          return true;\n        } else if (event.key === 'Enter' && event.shiftKey) {\n          // Shift+Enter - create line break and move cursor\n          event.preventDefault();\n          const br = jodit.createInside.element('br');\n          jodit.selection.insertNode(br);\n\n          // Ensure cursor moves after the <br> tag\n          const range = jodit.selection.createRange();\n          range.setStartAfter(br);\n          range.collapse(true);\n          jodit.selection.selectRange(range);\n          return false;\n        }\n        return true;\n      },\n      // Ensure cursor positioning after content changes\n      afterSetMode: jodit => {\n        // Maintain cursor position when switching modes\n        setTimeout(() => {\n          jodit.selection.focus();\n        }, 50);\n      },\n      // Handle content changes to maintain cursor position\n      change: (content, jodit) => {\n        // This will be handled by the onChange prop\n      }\n    },\n    controls: {\n      font: {\n        list: {\n          \"Poppins, sans-serif\": \"Poppins\",\n          \"Roboto, sans-serif\": \"Roboto\",\n          \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\n          \"Open Sans, sans-serif\": \"Open Sans\",\n          \"Calibri, sans-serif\": \"Calibri\",\n          \"Century Gothic, sans-serif\": \"Century Gothic\"\n        }\n      }\n    }\n  }), [isRtlDirection, toolbarVisibleRTEId]);\n\n  // Determine which containers to use based on guide type\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isAITour = createWithAI && selectedTemplate === \"Tour\";\n  const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\n  const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\n  const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement && !isTourAnnouncement) {\n    containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\n  } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n    var _toolTipGuideMetaData5;\n    if ((_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStepIndex]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.containers) {\n      containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\n    } else {\n      containersToRender = [];\n    }\n  } else {\n    containersToRender = rtesContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: containersToRender.map(item => {\n      let rteText = \"\";\n      let rteId = \"\";\n      let id = \"\";\n      if (isAIAnnouncement && !isTourAnnouncement || isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\n        rteText = item.rteBoxValue || \"\";\n        rteId = item.id;\n        id = item.id;\n      } else {\n        var _item$rtes, _item$rtes$, _item$rtes2, _item$rtes2$;\n        rteText = ((_item$rtes = item.rtes) === null || _item$rtes === void 0 ? void 0 : (_item$rtes$ = _item$rtes[0]) === null || _item$rtes$ === void 0 ? void 0 : _item$rtes$.text) || \"\";\n        rteId = (_item$rtes2 = item.rtes) === null || _item$rtes2 === void 0 ? void 0 : (_item$rtes2$ = _item$rtes2[0]) === null || _item$rtes2$ === void 0 ? void 0 : _item$rtes2$.id;\n        id = item.id;\n      }\n      if (!id) return null;\n      const currentContainerRef = getContainerRef(id);\n      const currentEditorRef = getEditorRef(id);\n      return /*#__PURE__*/_jsxDEV(Box, {\n        ref: currentContainerRef,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          position: \"relative\",\n          \"& .jodit-status-bar-link\": {\n            display: \"none !important\"\n          },\n          \"& .jodit-editor\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-editor span\": {\n            fontFamily: \"'Roboto', sans-serif !important\"\n          },\n          \".jodit-toolbar-button button\": {\n            minWidth: \"29px !important\"\n          },\n          \".jodit-react-container\": {\n            width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\n            whiteSpace: \"pre-wrap\",\n            wordBreak: \"break-word\"\n          },\n          \".jodit-workplace\": {\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : selectedTemplate === \"Announcement\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\" ? \"calc(100vh - 400px) !important\" : null,\n            overflow: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"hidden\" : \"auto !important\"\n          },\n          \".jodit-container\": {\n            minWidth: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null,\n            minHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"50px !important\" : null\n          },\n          \".jodit-toolbar__box\": {\n            display: \"flex !important\",\n            justifyContent: \"center !important\",\n            height: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null,\n            maxHeight: selectedTemplate === \"Banner\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\" ? \"32px !important\" : null\n          },\n          // Fix Jodit dialog positioning\n          \".jodit.jodit-dialog\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          },\n          \".jodit-dialog .jodit-dialog__panel\": {\n            position: \"relative !important\",\n            top: \"auto !important\",\n            left: \"auto !important\",\n            transform: \"none !important\",\n            maxWidth: \"400px !important\",\n            background: \"white !important\",\n            border: \"1px solid #ccc !important\",\n            borderRadius: \"4px !important\",\n            boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\n          },\n          \".jodit-dialog_alert\": {\n            position: \"fixed !important\",\n            zIndex: \"100001 !important\",\n            top: \"50% !important\",\n            left: \"50% !important\",\n            transform: \"translate(-50%, -50%) !important\"\n          }\n        },\n        className: \"qadpt-rte\",\n        children: selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\" ? /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleCloneContainer(item.id),\n              disabled: isCloneDisabled,\n              title: isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\"),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  height: \"24px\",\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: copyicon\n                },\n                style: {\n                  opacity: isCloneDisabled ? 0.5 : 1,\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 45\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => handleDeleteSection(item.id, rteId),\n              sx: {\n                \"&:hover\": {\n                  backgroundColor: \"transparent !important\"\n                },\n                svg: {\n                  path: {\n                    fill: \"var(--primarycolor)\"\n                  }\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: deleteicon\n                },\n                style: {\n                  height: '24px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 45\n            }, this)]\n          }, void 0, true),\n          placement: \"top\",\n          slotProps: {\n            tooltip: {\n              sx: {\n                backgroundColor: 'white',\n                color: 'black',\n                borderRadius: '4px',\n                padding: '0px 4px',\n                border: \"1px dashed var(--primarycolor)\",\n                marginBottom: '30px !important'\n              }\n            }\n          },\n          PopperProps: {\n            modifiers: [{\n              name: 'preventOverflow',\n              options: {\n                boundary: 'viewport'\n              }\n            }, {\n              name: 'flip',\n              options: {\n                enabled: true\n              }\n            }]\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"100%\",\n              position: \"relative\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n              ref: currentEditorRef,\n              value: rteText,\n              config: config,\n              onChange: newContent => handleUpdate(newContent, rteId, id),\n              onFocus: () => setEditingRTEId(id)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: e => {\n                e.stopPropagation();\n                toggleToolbar(id);\n              },\n              sx: {\n                position: \"absolute\",\n                bottom: \"2px\",\n                right: \"2px\",\n                width: \"24px\",\n                height: \"24px\",\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                zIndex: 1000,\n                \"&:hover\": {\n                  backgroundColor: \"rgba(255, 255, 255, 1)\"\n                },\n                \"& svg\": {\n                  width: \"16px\",\n                  height: \"16px\"\n                }\n              },\n              title: translate(\"Toggle Toolbar\"),\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: editicon\n                },\n                style: {\n                  height: '16px',\n                  width: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 37\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            position: \"relative\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(JoditEditor, {\n            ref: currentEditorRef,\n            value: rteText,\n            config: config,\n            onChange: newContent => handleUpdate(newContent, rteId, id),\n            onFocus: () => setEditingRTEId(id)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: e => {\n              e.stopPropagation();\n              toggleToolbar(id);\n            },\n            sx: {\n              position: \"absolute\",\n              bottom: \"2px\",\n              right: \"2px\",\n              width: \"24px\",\n              height: \"24px\",\n              backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n              zIndex: 1000,\n              \"&:hover\": {\n                backgroundColor: \"rgba(255, 255, 255, 1)\"\n              },\n              \"& svg\": {\n                width: \"16px\",\n                height: \"16px\"\n              }\n            },\n            title: translate(\"Toggle Toolbar\"),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: editicon\n              },\n              style: {\n                height: '16px',\n                width: '16px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 33\n        }, this)\n      }, id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 25\n      }, this);\n    })\n  }, void 0, false);\n}, \"XaGX863voH8B4EPgdEhb/gSgjjo=\", false, function () {\n  return [useTranslation, useDrawerStore];\n})), \"XaGX863voH8B4EPgdEhb/gSgjjo=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c2 = RTEsection;\nexport default RTEsection;\nvar _c, _c2;\n$RefreshReg$(_c, \"RTEsection$forwardRef\");\n$RefreshReg$(_c2, \"RTEsection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "forwardRef", "useMemo", "Box", "<PERSON><PERSON><PERSON>", "IconButton", "JoditEditor", "useDrawerStore", "copyicon", "deleteicon", "editicon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RTEsection", "_s", "_c", "textBoxRef", "isBanner", "handleDeleteRTESection", "index", "guidePopUpRef", "onClone", "isCloneDisabled", "ref", "t", "translate", "rtesContainer", "updateRTEContainer", "setIsUnSavedChanges", "cloneRTEContainer", "clearRteDetails", "selectedTemplate", "selectedTemplateTour", "announcementGuideMetaData", "toolTipGuideMetaData", "handleAnnouncementRTEValue", "handleTooltipRTEValue", "createWithAI", "currentStep", "ensureAnnouncementRTEContainer", "editingRTEId", "setEditingRTEId", "toolbarVisibleRTEId", "setToolbarVisibleRTEId", "hoveredRTEId", "setHoveredRTEId", "contentRef", "contentStates", "setContentStates", "Map", "editor<PERSON><PERSON><PERSON>", "containerRefs", "getEditorRef", "rteId", "current", "has", "set", "createRef", "get", "getContainerRef", "handleClickOutside", "event", "_document$querySelect", "_document$querySelect2", "_document$querySelect3", "_document$querySelect4", "isInsideJoditPopupContent", "target", "closest", "isInsideAltTextPopup", "isInsidePopup", "document", "querySelector", "contains", "isInsideJoditPopup", "isInsideWorkplacePopup", "isSelectionMarker", "id", "startsWith", "isLinkPopup", "isInsideToolbarButton", "isInsertButton", "currentContainerRef", "addEventListener", "removeEventListener", "editor<PERSON><PERSON>", "setTimeout", "joditInstance", "editor", "focus", "range", "selection", "createRange", "selectNodeContents", "collapse", "selectRange", "isContentEmpty", "content", "textContent", "replace", "trim", "length", "isContentScrollable", "containerId", "containerRef", "workplace", "scrollHeight", "clientHeight", "updateContentState", "isEmpty", "isScrollable", "prev", "newMap", "handleUpdate", "newContent", "isAIAnnouncement", "isAITour", "isTourAnnouncement", "isTourBanner", "isTourTooltip", "currentStepIndex", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "tooltipContainer", "containers", "find", "container", "type", "_announcementGuideMet", "_announcementGuideMet2", "announcementC<PERSON>r", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "handleCloneContainer", "handleDeleteSection", "handlePaste", "preventDefault", "clipboardData", "pastedText", "getData", "pastedHtml", "isRTEContent", "includes", "insertContent", "insertHTML", "toggleToolbar", "isRtlDirection", "setIsRtlDirection", "dir", "body", "getAttribute", "toLowerCase", "config", "readonly", "direction", "language", "toolbarSticky", "toolbarAdaptive", "toolbar", "enter", "enterBlock", "defaultMode", "cleanHTML", "fillEmptyParagraph", "replaceNBSP", "allowTags", "autofocus", "cursorAfterAutofocus", "useBRInsteadOfP", "disablePlugins", "commandToHotkeys", "buttons", "name", "iconURL", "list", "popupRoot", "zIndex", "globalFullSize", "mode", "link", "followOnDblClick", "processVideoLink", "processPastedLink", "openInNewTabCheckbox", "noFollowCheckbox", "modeClassName", "dialog", "events", "onPaste", "afterInit", "jodit", "isCollapsed", "keydown", "key", "shift<PERSON>ey", "br", "createInside", "element", "insertNode", "setStartAfter", "afterSetMode", "change", "controls", "font", "containersToRender", "_toolTipGuideMetaData5", "filter", "c", "children", "map", "item", "rteText", "rteBoxValue", "_item$rtes", "_item$rtes$", "_item$rtes2", "_item$rtes2$", "rtes", "text", "currentEditorRef", "sx", "display", "alignItems", "position", "fontFamily", "min<PERSON><PERSON><PERSON>", "width", "whiteSpace", "wordBreak", "minHeight", "maxHeight", "overflow", "justifyContent", "height", "top", "left", "transform", "max<PERSON><PERSON><PERSON>", "background", "border", "borderRadius", "boxShadow", "className", "title", "size", "onClick", "disabled", "backgroundColor", "svg", "path", "fill", "dangerouslySetInnerHTML", "__html", "style", "opacity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placement", "slotProps", "tooltip", "color", "padding", "marginBottom", "PopperProps", "modifiers", "options", "boundary", "enabled", "value", "onChange", "onFocus", "e", "stopPropagation", "bottom", "right", "_c2", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/RTEsection.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, forwardRef, useMemo } from \"react\";\r\nimport { Box, TextField, Tooltip, IconButton } from \"@mui/material\";\r\nimport JoditEditor from \"jodit-react\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport { copyicon, deleteicon, editicon } from \"../../../assets/icons/icons\";\r\nimport { selectedtemp } from \"../../drawer/Drawer\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ninterface RTEsectionProps {\r\n    textBoxRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    guidePopUpRef: React.MutableRefObject<HTMLDivElement | null>;\r\n    isBanner: boolean;\r\n    handleDeleteRTESection: (params: number) => void;\r\n    index: number;\r\n    onClone?: () => void;\r\n    isCloneDisabled?: boolean;\r\n}\r\n\r\nconst RTEsection: React.FC<RTEsectionProps> = forwardRef(\r\n    ({ textBoxRef, isBanner, handleDeleteRTESection, index, guidePopUpRef, onClone, isCloneDisabled }, ref) => {\r\n        const { t: translate } = useTranslation();\r\n        const {\r\n            rtesContainer,\r\n            updateRTEContainer,\r\n            setIsUnSavedChanges,\r\n            cloneRTEContainer,\r\n            clearRteDetails,\r\n            selectedTemplate,\r\n            selectedTemplateTour,\r\n            announcementGuideMetaData,\r\n            toolTipGuideMetaData,\r\n            handleAnnouncementRTEValue,\r\n            handleTooltipRTEValue,\r\n            createWithAI,\r\n            currentStep,\r\n            ensureAnnouncementRTEContainer\r\n        } = useDrawerStore();\r\n\r\n        // Individual state management for each RTE\r\n        const [editingRTEId, setEditingRTEId] = useState<string | null>(null);\r\n        const [toolbarVisibleRTEId, setToolbarVisibleRTEId] = useState<string | null>(null);\r\n        const [hoveredRTEId, setHoveredRTEId] = useState<string | null>(null);\r\n        const contentRef = useRef<string>(\"\");\r\n        const [contentStates, setContentStates] = useState<Map<string, { isEmpty: boolean, isScrollable: boolean }>>(new Map());\r\n        \r\n        // Map to store individual refs for each RTE\r\n        const editorRefs = useRef<Map<string, React.RefObject<any>>>(new Map());\r\n        const containerRefs = useRef<Map<string, React.RefObject<HTMLDivElement>>>(new Map());\r\n\r\n        // Helper function to get or create editor ref for specific RTE\r\n        const getEditorRef = (rteId: string) => {\r\n            if (!editorRefs.current.has(rteId)) {\r\n                editorRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return editorRefs.current.get(rteId);\r\n        };\r\n\r\n        // Helper function to get or create container ref for specific RTE\r\n        const getContainerRef = (rteId: string) => {\r\n            if (!containerRefs.current.has(rteId)) {\r\n                containerRefs.current.set(rteId, React.createRef());\r\n            }\r\n            return containerRefs.current.get(rteId);\r\n        };\r\n\r\n        // Handle clicks outside the editor\r\n        useEffect(() => {\r\n            const handleClickOutside = (event: MouseEvent) => {\r\n                if (!editingRTEId) return;\r\n\r\n                const isInsideJoditPopupContent = (event.target as HTMLElement).closest(\".jodit-popup__content\") !== null;\r\n                const isInsideAltTextPopup = (event.target as HTMLElement).closest(\".jodit-ui-input\") !== null;\r\n                const isInsidePopup = document.querySelector(\".jodit-popup\")?.contains(event.target as Node);\r\n                const isInsideJoditPopup = document.querySelector(\".jodit-wysiwyg\")?.contains(event.target as Node);\r\n                const isInsideWorkplacePopup = isInsideJoditPopup || document.querySelector(\".jodit-dialog__panel\")?.contains(event.target as Node);\r\n                const isSelectionMarker = (event.target as HTMLElement).id.startsWith(\"jodit-selection_marker_\");\r\n                const isLinkPopup = document.querySelector(\".jodit-ui-input__input\")?.contains(event.target as Node);\r\n                const isInsideToolbarButton = (event.target as HTMLElement).closest(\".jodit-toolbar-button__button\") !== null;\r\n                const isInsertButton = (event.target as HTMLElement).closest(\"button[aria-pressed='false']\") !== null;\r\n\r\n                const currentContainerRef = getContainerRef(editingRTEId);\r\n\r\n                if (\r\n                    currentContainerRef?.current &&\r\n                    !currentContainerRef.current.contains(event.target as Node) &&\r\n                    !isInsidePopup &&\r\n                    !isInsideJoditPopup &&\r\n                    !isInsideWorkplacePopup &&\r\n                    !isSelectionMarker &&\r\n                    !isLinkPopup &&\r\n                    !isInsideToolbarButton &&\r\n                    !isInsertButton &&\r\n                    !isInsideJoditPopupContent &&\r\n                    !isInsideAltTextPopup\r\n                ) {\r\n                    setEditingRTEId(null);\r\n                    setToolbarVisibleRTEId(null);\r\n                }\r\n            };\r\n\r\n            document.addEventListener(\"mousedown\", handleClickOutside);\r\n            return () => document.removeEventListener(\"mousedown\", handleClickOutside);\r\n        }, [editingRTEId]);\r\n\r\n        useEffect(() => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    setTimeout(() => {\r\n                        const joditInstance = (editorRef.current as any).editor;\r\n                        if (joditInstance) {\r\n                            // Focus the editor\r\n                            joditInstance.focus();\r\n                            \r\n                            // Move cursor to end of content instead of beginning\r\n                            const range = joditInstance.selection.createRange();\r\n                            range.selectNodeContents(joditInstance.editor);\r\n                            range.collapse(false); // false = collapse to end, true = collapse to start\r\n                            joditInstance.selection.selectRange(range);\r\n                        }\r\n                    }, 100); // Increased timeout for better reliability\r\n                }\r\n            }\r\n        }, [editingRTEId]);\r\n\r\n        // Helper function to check if content is empty\r\n        const isContentEmpty = (content: string): boolean => {\r\n            if (!content) return true;\r\n            const textContent = content.replace(/<[^>]*>/g, '').trim();\r\n            return textContent.length === 0;\r\n        };\r\n\r\n        // Helper function to check if content is scrollable\r\n        const isContentScrollable = (containerId: string): boolean => {\r\n            const containerRef = getContainerRef(containerId);\r\n            if (containerRef?.current) {\r\n                const workplace = containerRef.current.querySelector('.jodit-workplace');\r\n                if (workplace) {\r\n                    return workplace.scrollHeight > workplace.clientHeight;\r\n                }\r\n            }\r\n            return false;\r\n        };\r\n\r\n        // Update content state for dynamic icon positioning\r\n        const updateContentState = (containerId: string, content: string) => {\r\n            const isEmpty = isContentEmpty(content);\r\n            const isScrollable = isContentScrollable(containerId);\r\n\r\n            setContentStates(prev => {\r\n                const newMap = new Map(prev);\r\n                newMap.set(containerId, { isEmpty, isScrollable });\r\n                return newMap;\r\n            });\r\n        };\r\n\r\n        const handleUpdate = (newContent: string, rteId: string, containerId: string) => {\r\n            contentRef.current = newContent;\r\n\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n            const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n            const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n            const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n            const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n\r\n            if (isAIAnnouncement) {\r\n                const currentStepIndex = currentStep - 1;\r\n\r\n                if (isTourAnnouncement) {\r\n                    const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (tooltipContainer) {\r\n                        handleTooltipRTEValue(containerId, newContent);\r\n                    }\r\n                } else {\r\n                    const announcementContainer = announcementGuideMetaData[currentStepIndex]?.containers?.find(\r\n                        (container: any) => container.id === containerId && container.type === \"rte\"\r\n                    );\r\n\r\n                    if (announcementContainer) {\r\n                        handleAnnouncementRTEValue(containerId, newContent);\r\n                    }\r\n                }\r\n            } else if (isAITour && (isTourBanner || isTourTooltip)) {\r\n                const currentStepIndex = currentStep - 1;\r\n                const tooltipContainer = toolTipGuideMetaData[currentStepIndex]?.containers?.find(\r\n                    (container: any) => container.id === containerId && container.type === \"rte\"\r\n                );\r\n\r\n                if (tooltipContainer) {\r\n                    handleTooltipRTEValue(containerId, newContent);\r\n                }\r\n            } else {\r\n                updateRTEContainer(containerId, rteId, newContent);\r\n            }\r\n\r\n            setIsUnSavedChanges(true);\r\n            updateContentState(containerId, newContent);\r\n        };\r\n\r\n        const handleCloneContainer = (containerId: string) => {\r\n            if (isCloneDisabled) {\r\n                return;\r\n            }\r\n            cloneRTEContainer(containerId);\r\n            if (onClone) {\r\n                onClone();\r\n            }\r\n        };\r\n\r\n        const handleDeleteSection = (containerId: string, rteId: string) => {\r\n            const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\r\n            if (isAIAnnouncement) {\r\n                clearRteDetails(containerId, rteId);\r\n            } else {\r\n                clearRteDetails(containerId, rteId);\r\n            }\r\n\r\n            handleDeleteRTESection(index);\r\n        };\r\n\r\n        const handlePaste = (event: React.ClipboardEvent<HTMLDivElement>) => {\r\n            event.preventDefault();\r\n\r\n            const clipboardData = event.clipboardData;\r\n            const pastedText = clipboardData.getData(\"text/plain\");\r\n            const pastedHtml = clipboardData.getData(\"text/html\");\r\n\r\n            if (pastedHtml) {\r\n                const isRTEContent = pastedHtml.includes(\"<!--RTE-->\");\r\n                if (isRTEContent) {\r\n                    insertContent(pastedHtml);\r\n                } else {\r\n                    insertContent(pastedHtml);\r\n                }\r\n            } else {\r\n                insertContent(pastedText);\r\n            }\r\n        };\r\n\r\n        const insertContent = (content: string) => {\r\n            if (editingRTEId) {\r\n                const editorRef = getEditorRef(editingRTEId);\r\n                if (editorRef?.current) {\r\n                    const editor = (editorRef.current as any).editor;\r\n                    editor.selection.insertHTML(content);\r\n                }\r\n            }\r\n        };\r\n\r\n        const toggleToolbar = (rteId: string) => {\r\n            if (toolbarVisibleRTEId === rteId) {\r\n                setToolbarVisibleRTEId(null);\r\n            } else {\r\n                setToolbarVisibleRTEId(rteId);\r\n            }\r\n        };\r\n\r\n        const [isRtlDirection, setIsRtlDirection] = useState<boolean>(false);\r\n        \r\n        useEffect(() => {\r\n            const dir = document.body.getAttribute(\"dir\") || \"ltr\";\r\n            setIsRtlDirection(dir.toLowerCase() === \"rtl\");\r\n        }, []);\r\n\r\n        // FIXED JODIT CONFIG - Addresses cursor position and Enter key issues\r\n        const config = useMemo(\r\n            (): any => ({\r\n                readonly: false,\r\n                direction: isRtlDirection ? 'rtl' as const : 'ltr' as const,\r\n                language: 'en',\r\n                toolbarSticky: false,\r\n                toolbarAdaptive: false,\r\n                toolbar: toolbarVisibleRTEId !== null,\r\n                \r\n                // Enhanced paragraph and line behavior\r\n                enter: 'P', // Creates <p> tags on Enter\r\n                enterBlock: 'p', // Block element to create on Enter\r\n                defaultMode: 1, // WYSIWYG mode\r\n                \r\n                // Ensure proper paragraph behavior\r\n                cleanHTML: {\r\n                    fillEmptyParagraph: true, // Fill empty paragraphs with &nbsp;\r\n                    replaceNBSP: false, // Keep non-breaking spaces\r\n                    allowTags: true\r\n                },\r\n                \r\n                // Critical cursor positioning fixes\r\n                autofocus: false, // Disable autofocus to prevent cursor jumping\r\n                cursorAfterAutofocus: 'end' as const, // When focused, cursor goes to end\r\n                \r\n                // Line break behavior\r\n                useBRInsteadOfP: false, // Use <p> tags instead of <br>\r\n                \r\n                // Prevent plugins that might interfere\r\n                disablePlugins: ['preview'], // Disable potentially problematic plugins\r\n                \r\n                // Keyboard shortcuts\r\n                commandToHotkeys: {\r\n                    'insertLineBreak': 'shift+enter', // Shift+Enter for <br>\r\n                    'insertParagraph': 'enter' // Enter for new paragraph\r\n                },\r\n\r\n                buttons: [\r\n                    'bold', 'italic', 'underline', 'strikethrough', 'ul', 'ol', 'brush',\r\n                    'font', 'fontsize', 'link',\r\n                    {\r\n                        name: 'more',\r\n                        iconURL: 'https://cdn.jsdelivr.net/npm/jodit/build/icons/three-dots.svg',\r\n                        list: [\r\n                            'source',\r\n                            'image', 'video', 'table',\r\n                            'align', 'undo', 'redo', '|',\r\n                            'hr', 'eraser', 'copyformat',\r\n                            'symbol', 'fullsize', 'print', 'superscript', 'subscript', '|',\r\n                            'outdent', 'indent', 'paragraph',\r\n                        ]\r\n                    }\r\n                ],\r\n                \r\n                popupRoot: document.body,\r\n                zIndex: 100000,\r\n                globalFullSize: false,\r\n                \r\n                // Fix selection and cursor behavior\r\n                selection: {\r\n                    mode: 'single' as const\r\n                },\r\n                \r\n                link: {\r\n                    followOnDblClick: false,\r\n                    processVideoLink: true,\r\n                    processPastedLink: true,\r\n                    openInNewTabCheckbox: true,\r\n                    noFollowCheckbox: false,\r\n                    modeClassName: 'input' as const,\r\n                },\r\n                \r\n                dialog: {\r\n                    zIndex: 100001,\r\n                },\r\n                \r\n                // CRITICAL: Enhanced event handling\r\n                events: {\r\n                    // Custom paste handler\r\n                    onPaste: handlePaste,\r\n                    \r\n                    // Fix cursor positioning after initialization\r\n                    afterInit: (jodit: any) => {\r\n                        // Don't auto-focus immediately\r\n                        setTimeout(() => {\r\n                            const editor = jodit.editor;\r\n                            if (editor) {\r\n                                // Set cursor to end of content\r\n                                const range = jodit.selection.createRange();\r\n                                range.selectNodeContents(editor);\r\n                                range.collapse(false); // false = collapse to end\r\n                                jodit.selection.selectRange(range);\r\n                            }\r\n                        }, 100);\r\n                    },\r\n                    \r\n                    // Handle focus events to maintain cursor position\r\n                    focus: (jodit: any) => {\r\n                        // Maintain cursor position on focus\r\n                        setTimeout(() => {\r\n                            const selection = jodit.selection;\r\n                            if (selection && !selection.isCollapsed()) {\r\n                                // If there's no selection, move to end of content\r\n                                const range = selection.createRange();\r\n                                range.selectNodeContents(jodit.editor);\r\n                                range.collapse(false);\r\n                                selection.selectRange(range);\r\n                            }\r\n                        }, 0);\r\n                    },\r\n                    \r\n                    // Ensure Enter key works properly\r\n                    keydown: (event: KeyboardEvent, jodit: any) => {\r\n                        if (event.key === 'Enter' && !event.shiftKey) {\r\n                            // Normal Enter - create new paragraph and ensure cursor moves\r\n                            // Let Jodit handle this naturally, it will create <p> and move cursor\r\n                            return true;\r\n                        } else if (event.key === 'Enter' && event.shiftKey) {\r\n                            // Shift+Enter - create line break and move cursor\r\n                            event.preventDefault();\r\n                            const br = jodit.createInside.element('br');\r\n                            jodit.selection.insertNode(br);\r\n                            \r\n                            // Ensure cursor moves after the <br> tag\r\n                            const range = jodit.selection.createRange();\r\n                            range.setStartAfter(br);\r\n                            range.collapse(true);\r\n                            jodit.selection.selectRange(range);\r\n                            \r\n                            return false;\r\n                        }\r\n                        return true;\r\n                    },\r\n                    \r\n                    // Ensure cursor positioning after content changes\r\n                    afterSetMode: (jodit: any) => {\r\n                        // Maintain cursor position when switching modes\r\n                        setTimeout(() => {\r\n                            jodit.selection.focus();\r\n                        }, 50);\r\n                    },\r\n                    \r\n                    // Handle content changes to maintain cursor position\r\n                    change: (content: string, jodit: any) => {\r\n                        // This will be handled by the onChange prop\r\n                    }\r\n                },\r\n                \r\n                controls: {\r\n                    font: {\r\n                        list: {\r\n                            \"Poppins, sans-serif\": \"Poppins\",\r\n                            \"Roboto, sans-serif\": \"Roboto\",\r\n                            \"Comic Sans MS, sans-serif\": \"Comic Sans MS\",\r\n                            \"Open Sans, sans-serif\": \"Open Sans\",\r\n                            \"Calibri, sans-serif\": \"Calibri\",\r\n                            \"Century Gothic, sans-serif\": \"Century Gothic\",\r\n                        }\r\n                    }\r\n                }\r\n            }),\r\n            [isRtlDirection, toolbarVisibleRTEId]\r\n        );\r\n\r\n        // Determine which containers to use based on guide type\r\n        const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n        const isAITour = createWithAI && selectedTemplate === \"Tour\";\r\n        const isTourAnnouncement = isAITour && selectedTemplateTour === \"Announcement\";\r\n        const isTourBanner = isAITour && selectedTemplateTour === \"Banner\";\r\n        const isTourTooltip = isAITour && (selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\");\r\n        const currentStepIndex = currentStep - 1;\r\n\r\n        let containersToRender: any[] = [];\r\n\r\n        if (isAIAnnouncement && !isTourAnnouncement) {\r\n            containersToRender = ensureAnnouncementRTEContainer(currentStepIndex, false);\r\n        } else if (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement)) {\r\n            if (toolTipGuideMetaData[currentStepIndex]?.containers) {\r\n                containersToRender = toolTipGuideMetaData[currentStepIndex].containers.filter(c => c.type === \"rte\");\r\n            } else {\r\n                containersToRender = [];\r\n            }\r\n        } else {\r\n            containersToRender = rtesContainer;\r\n        }\r\n\r\n        return (\r\n            <>\r\n                {containersToRender.map((item: any) => {\r\n                    let rteText = \"\";\r\n                    let rteId = \"\";\r\n                    let id = \"\";\r\n\r\n                    if ((isAIAnnouncement && !isTourAnnouncement) || (isAITour && (isTourBanner || isTourTooltip || isTourAnnouncement))) {\r\n                        rteText = item.rteBoxValue || \"\";\r\n                        rteId = item.id;\r\n                        id = item.id;\r\n                    } else {\r\n                        rteText = item.rtes?.[0]?.text || \"\";\r\n                        rteId = item.rtes?.[0]?.id;\r\n                        id = item.id;\r\n                    }\r\n\r\n                    if (!id) return null;\r\n\r\n                    const currentContainerRef = getContainerRef(id);\r\n                    const currentEditorRef = getEditorRef(id);\r\n\r\n                    return (\r\n                        <Box\r\n                            key={id}\r\n                            ref={currentContainerRef}\r\n                            sx={{\r\n                                display: \"flex\",\r\n                                alignItems: \"center\",\r\n                                position: \"relative\",\r\n                                \"& .jodit-status-bar-link\": {\r\n                                    display: \"none !important\",\r\n                                },\r\n                                \"& .jodit-editor\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-editor span\": {\r\n                                    fontFamily: \"'Roboto', sans-serif !important\",\r\n                                },\r\n                                \".jodit-toolbar-button button\": {\r\n                                    minWidth: \"29px !important\",\r\n                                },\r\n                                \".jodit-react-container\": {\r\n                                    width: selectedTemplate === \"Banner\" ? \"100%\" : \"100%\",\r\n                                    whiteSpace: \"pre-wrap\",\r\n                                    wordBreak: \"break-word\",\r\n                                },\r\n                                \".jodit-workplace\": {\r\n                                    minHeight: selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\" : null,\r\n                                    maxHeight: (\r\n                                        selectedTemplate === \"Banner\" ||\r\n                                        (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\")\r\n                                    )\r\n                                        ? \"50px !important\"\r\n                                        : (\r\n                                            selectedTemplate === \"Announcement\" ||\r\n                                            (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\")\r\n                                        )\r\n                                            ? \"calc(100vh - 400px) !important\"\r\n                                            : null,\r\n                                    overflow: selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"hidden\" : \"auto !important\",\r\n                                },\r\n                                \".jodit-container\": {\r\n                                    minWidth: selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\" : null,\r\n                                    minHeight: selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"50px !important\" : null\r\n                                },\r\n                                \".jodit-toolbar__box\": {\r\n                                    display: \"flex !important\",\r\n                                    justifyContent: \"center !important\",\r\n                                    height: selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\" : null,\r\n                                    maxHeight: selectedTemplate === \"Banner\" || (selectedTemplate === \"Tour\" && selectedTemplateTour === \"Banner\") ? \"32px !important\" : null\r\n                                },\r\n                                // Fix Jodit dialog positioning\r\n                                \".jodit.jodit-dialog\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                },\r\n                                \".jodit-dialog .jodit-dialog__panel\": {\r\n                                    position: \"relative !important\",\r\n                                    top: \"auto !important\",\r\n                                    left: \"auto !important\",\r\n                                    transform: \"none !important\",\r\n                                    maxWidth: \"400px !important\",\r\n                                    background: \"white !important\",\r\n                                    border: \"1px solid #ccc !important\",\r\n                                    borderRadius: \"4px !important\",\r\n                                    boxShadow: \"0 4px 12px rgba(0,0,0,0.15) !important\"\r\n                                },\r\n                                \".jodit-dialog_alert\": {\r\n                                    position: \"fixed !important\",\r\n                                    zIndex: \"100001 !important\",\r\n                                    top: \"50% !important\",\r\n                                    left: \"50% !important\",\r\n                                    transform: \"translate(-50%, -50%) !important\"\r\n                                }\r\n                            }}\r\n                            className=\"qadpt-rte\"\r\n                        >\r\n                            {(selectedTemplate === \"Announcement\" || selectedTemplate === \"Tooltip\" || selectedTemplate === \"Hotspot\") || (selectedTemplateTour === \"Announcement\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Hotspot\") ? (\r\n                                <Tooltip\r\n                                    title={\r\n                                        <>\r\n                                            <IconButton\r\n                                                size=\"small\"\r\n                                                onClick={() => handleCloneContainer(item.id)}\r\n                                                disabled={isCloneDisabled}\r\n                                                title={isCloneDisabled ? translate(\"Maximum limit of 3 Rich Text sections reached\") : translate(\"Clone Section\")}\r\n                                                sx={{\r\n                                                    \"&:hover\": {\r\n                                                        backgroundColor: \"transparent !important\",\r\n                                                    },\r\n                                                    svg: {\r\n                                                        height: \"24px\",\r\n                                                        path: {\r\n                                                            fill: \"var(--primarycolor)\"\r\n                                                        }\r\n                                                    },\r\n                                                }}\r\n                                            >\r\n                                                <span\r\n                                                    dangerouslySetInnerHTML={{ __html: copyicon }}\r\n                                                    style={{\r\n                                                        opacity: isCloneDisabled ? 0.5 : 1,\r\n                                                        height: '24px'\r\n                                                    }}\r\n                                                />\r\n                                            </IconButton>\r\n                                            <IconButton size=\"small\" onClick={() => handleDeleteSection(item.id, rteId)}\r\n                                                sx={{\r\n                                                    \"&:hover\": {\r\n                                                        backgroundColor: \"transparent !important\",\r\n                                                    },\r\n                                                    svg: {\r\n                                                        path: {\r\n                                                            fill: \"var(--primarycolor)\"\r\n                                                        }\r\n                                                    },\r\n                                                }}\r\n                                            >\r\n                                                <span dangerouslySetInnerHTML={{ __html: deleteicon }}\r\n                                                    style={{\r\n                                                        height: '24px'\r\n                                                    }}\r\n                                                />\r\n                                            </IconButton>\r\n                                        </>\r\n                                    }\r\n                                    placement=\"top\"\r\n                                    slotProps={{\r\n                                        tooltip: {\r\n                                            sx: {\r\n                                                backgroundColor: 'white',\r\n                                                color: 'black',\r\n                                                borderRadius: '4px',\r\n                                                padding: '0px 4px',\r\n                                                border: \"1px dashed var(--primarycolor)\",\r\n                                                marginBottom: '30px !important'\r\n                                            },\r\n                                        },\r\n                                    }}\r\n                                    PopperProps={{\r\n                                        modifiers: [\r\n                                            {\r\n                                                name: 'preventOverflow',\r\n                                                options: { boundary: 'viewport' },\r\n                                            },\r\n                                            {\r\n                                                name: 'flip',\r\n                                                options: { enabled: true },\r\n                                            },\r\n                                        ],\r\n                                    }}\r\n                                >\r\n                                    <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                        <JoditEditor\r\n                                            ref={currentEditorRef}\r\n                                            value={rteText}\r\n                                            config={config}\r\n                                            onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                            onFocus={() => setEditingRTEId(id)}\r\n                                        />\r\n                                        <IconButton\r\n                                            size=\"small\"\r\n                                            onClick={(e) => {\r\n                                                e.stopPropagation();\r\n                                                toggleToolbar(id);\r\n                                            }}\r\n                                            sx={{\r\n                                                position: \"absolute\",\r\n                                                bottom: \"2px\",\r\n                                                right: \"2px\",\r\n                                                width: \"24px\",\r\n                                                height: \"24px\",\r\n                                                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                                zIndex: 1000,\r\n                                                \"&:hover\": {\r\n                                                    backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                                },\r\n                                                \"& svg\": {\r\n                                                    width: \"16px\",\r\n                                                    height: \"16px\",\r\n                                                }\r\n                                            }}\r\n                                            title={translate(\"Toggle Toolbar\")}\r\n                                        >\r\n                                            <span\r\n                                                dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                                style={{ height: '16px', width: '16px' }}\r\n                                            />\r\n                                        </IconButton>\r\n                                    </div>\r\n                                </Tooltip>\r\n                            ) : (\r\n                                <div style={{ width: \"100%\", position: \"relative\" }}>\r\n                                    <JoditEditor\r\n                                        ref={currentEditorRef}\r\n                                        value={rteText}\r\n                                        config={config}\r\n                                        onChange={(newContent) => handleUpdate(newContent, rteId, id)}\r\n                                        onFocus={() => setEditingRTEId(id)}\r\n                                    />\r\n                                    <IconButton\r\n                                        size=\"small\"\r\n                                        onClick={(e) => {\r\n                                            e.stopPropagation();\r\n                                            toggleToolbar(id);\r\n                                        }}\r\n                                        sx={{\r\n                                            position: \"absolute\",\r\n                                            bottom: \"2px\",\r\n                                            right: \"2px\",\r\n                                            width: \"24px\",\r\n                                            height: \"24px\",\r\n                                            backgroundColor: \"rgba(255, 255, 255, 0.9)\",\r\n                                            zIndex: 1000,\r\n                                            \"&:hover\": {\r\n                                                backgroundColor: \"rgba(255, 255, 255, 1)\",\r\n                                            },\r\n                                            \"& svg\": {\r\n                                                width: \"16px\",\r\n                                                height: \"16px\",\r\n                                            }\r\n                                        }}\r\n                                        title={translate(\"Toggle Toolbar\")}\r\n                                    >\r\n                                        <span\r\n                                            dangerouslySetInnerHTML={{ __html: editicon }}\r\n                                            style={{ height: '16px', width: '16px' }}\r\n                                        />\r\n                                    </IconButton>\r\n                                </div>\r\n                            )}\r\n                        </Box>\r\n                    );\r\n                })}\r\n            </>\r\n        );\r\n    }\r\n);\r\n\r\nexport default RTEsection;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC/E,SAASC,GAAG,EAAaC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACnE,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,6BAA6B;AAE5E,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAY/C,MAAMC,UAAqC,gBAAAC,EAAA,cAAGhB,UAAU,CAAAiB,EAAA,GAAAD,EAAA,CACpD,CAAC;EAAEE,UAAU;EAAEC,QAAQ;EAAEC,sBAAsB;EAAEC,KAAK;EAAEC,aAAa;EAAEC,OAAO;EAAEC;AAAgB,CAAC,EAAEC,GAAG,KAAK;EAAAT,EAAA;EACvG,MAAM;IAAEU,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EACzC,MAAM;IACFkB,aAAa;IACbC,kBAAkB;IAClBC,mBAAmB;IACnBC,iBAAiB;IACjBC,eAAe;IACfC,gBAAgB;IAChBC,oBAAoB;IACpBC,yBAAyB;IACzBC,oBAAoB;IACpBC,0BAA0B;IAC1BC,qBAAqB;IACrBC,YAAY;IACZC,WAAW;IACXC;EACJ,CAAC,GAAGnC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+C,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhD,QAAQ,CAAgB,IAAI,CAAC;EACnF,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAMmD,UAAU,GAAGjD,MAAM,CAAS,EAAE,CAAC;EACrC,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAA2D,IAAIsD,GAAG,CAAC,CAAC,CAAC;;EAEvH;EACA,MAAMC,UAAU,GAAGrD,MAAM,CAAoC,IAAIoD,GAAG,CAAC,CAAC,CAAC;EACvE,MAAME,aAAa,GAAGtD,MAAM,CAA+C,IAAIoD,GAAG,CAAC,CAAC,CAAC;;EAErF;EACA,MAAMG,YAAY,GAAIC,KAAa,IAAK;IACpC,IAAI,CAACH,UAAU,CAACI,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MAChCH,UAAU,CAACI,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE3D,KAAK,CAAC+D,SAAS,CAAC,CAAC,CAAC;IACpD;IACA,OAAOP,UAAU,CAACI,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMM,eAAe,GAAIN,KAAa,IAAK;IACvC,IAAI,CAACF,aAAa,CAACG,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;MACnCF,aAAa,CAACG,OAAO,CAACE,GAAG,CAACH,KAAK,eAAE3D,KAAK,CAAC+D,SAAS,CAAC,CAAC,CAAC;IACvD;IACA,OAAON,aAAa,CAACG,OAAO,CAACI,GAAG,CAACL,KAAK,CAAC;EAC3C,CAAC;;EAED;EACAzD,SAAS,CAAC,MAAM;IACZ,MAAMgE,kBAAkB,GAAIC,KAAiB,IAAK;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MAC9C,IAAI,CAACzB,YAAY,EAAE;MAEnB,MAAM0B,yBAAyB,GAAIL,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,uBAAuB,CAAC,KAAK,IAAI;MACzG,MAAMC,oBAAoB,GAAIR,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,iBAAiB,CAAC,KAAK,IAAI;MAC9F,MAAME,aAAa,IAAAR,qBAAA,GAAGS,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,cAAAV,qBAAA,uBAAtCA,qBAAA,CAAwCW,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MAC5F,MAAMO,kBAAkB,IAAAX,sBAAA,GAAGQ,QAAQ,CAACC,aAAa,CAAC,gBAAgB,CAAC,cAAAT,sBAAA,uBAAxCA,sBAAA,CAA0CU,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnG,MAAMQ,sBAAsB,GAAGD,kBAAkB,MAAAV,sBAAA,GAAIO,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC,cAAAR,sBAAA,uBAA9CA,sBAAA,CAAgDS,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACnI,MAAMS,iBAAiB,GAAIf,KAAK,CAACM,MAAM,CAAiBU,EAAE,CAACC,UAAU,CAAC,yBAAyB,CAAC;MAChG,MAAMC,WAAW,IAAAd,sBAAA,GAAGM,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,cAAAP,sBAAA,uBAAhDA,sBAAA,CAAkDQ,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC;MACpG,MAAMa,qBAAqB,GAAInB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,+BAA+B,CAAC,KAAK,IAAI;MAC7G,MAAMa,cAAc,GAAIpB,KAAK,CAACM,MAAM,CAAiBC,OAAO,CAAC,8BAA8B,CAAC,KAAK,IAAI;MAErG,MAAMc,mBAAmB,GAAGvB,eAAe,CAACnB,YAAY,CAAC;MAEzD,IACI0C,mBAAmB,aAAnBA,mBAAmB,eAAnBA,mBAAmB,CAAE5B,OAAO,IAC5B,CAAC4B,mBAAmB,CAAC5B,OAAO,CAACmB,QAAQ,CAACZ,KAAK,CAACM,MAAc,CAAC,IAC3D,CAACG,aAAa,IACd,CAACI,kBAAkB,IACnB,CAACC,sBAAsB,IACvB,CAACC,iBAAiB,IAClB,CAACG,WAAW,IACZ,CAACC,qBAAqB,IACtB,CAACC,cAAc,IACf,CAACf,yBAAyB,IAC1B,CAACG,oBAAoB,EACvB;QACE5B,eAAe,CAAC,IAAI,CAAC;QACrBE,sBAAsB,CAAC,IAAI,CAAC;MAChC;IACJ,CAAC;IAED4B,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEvB,kBAAkB,CAAC;IAC1D,OAAO,MAAMW,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAExB,kBAAkB,CAAC;EAC9E,CAAC,EAAE,CAACpB,YAAY,CAAC,CAAC;EAElB5C,SAAS,CAAC,MAAM;IACZ,IAAI4C,YAAY,EAAE;MACd,MAAM6C,SAAS,GAAGjC,YAAY,CAACZ,YAAY,CAAC;MAC5C,IAAI6C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpBgC,UAAU,CAAC,MAAM;UACb,MAAMC,aAAa,GAAIF,SAAS,CAAC/B,OAAO,CAASkC,MAAM;UACvD,IAAID,aAAa,EAAE;YACf;YACAA,aAAa,CAACE,KAAK,CAAC,CAAC;;YAErB;YACA,MAAMC,KAAK,GAAGH,aAAa,CAACI,SAAS,CAACC,WAAW,CAAC,CAAC;YACnDF,KAAK,CAACG,kBAAkB,CAACN,aAAa,CAACC,MAAM,CAAC;YAC9CE,KAAK,CAACI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YACvBP,aAAa,CAACI,SAAS,CAACI,WAAW,CAACL,KAAK,CAAC;UAC9C;QACJ,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACb;IACJ;EACJ,CAAC,EAAE,CAAClD,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMwD,cAAc,GAAIC,OAAe,IAAc;IACjD,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IACzB,MAAMC,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;IAC1D,OAAOF,WAAW,CAACG,MAAM,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAIC,WAAmB,IAAc;IAC1D,MAAMC,YAAY,GAAG7C,eAAe,CAAC4C,WAAW,CAAC;IACjD,IAAIC,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAElD,OAAO,EAAE;MACvB,MAAMmD,SAAS,GAAGD,YAAY,CAAClD,OAAO,CAACkB,aAAa,CAAC,kBAAkB,CAAC;MACxE,IAAIiC,SAAS,EAAE;QACX,OAAOA,SAAS,CAACC,YAAY,GAAGD,SAAS,CAACE,YAAY;MAC1D;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAACL,WAAmB,EAAEN,OAAe,KAAK;IACjE,MAAMY,OAAO,GAAGb,cAAc,CAACC,OAAO,CAAC;IACvC,MAAMa,YAAY,GAAGR,mBAAmB,CAACC,WAAW,CAAC;IAErDvD,gBAAgB,CAAC+D,IAAI,IAAI;MACrB,MAAMC,MAAM,GAAG,IAAI/D,GAAG,CAAC8D,IAAI,CAAC;MAC5BC,MAAM,CAACxD,GAAG,CAAC+C,WAAW,EAAE;QAAEM,OAAO;QAAEC;MAAa,CAAC,CAAC;MAClD,OAAOE,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACC,UAAkB,EAAE7D,KAAa,EAAEkD,WAAmB,KAAK;IAC7EzD,UAAU,CAACQ,OAAO,GAAG4D,UAAU;IAE/B,MAAMC,gBAAgB,GAAG9E,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IACzH,MAAMoF,QAAQ,GAAG/E,YAAY,IAAIN,gBAAgB,KAAK,MAAM;IAC5D,MAAMsF,kBAAkB,GAAGD,QAAQ,IAAIpF,oBAAoB,KAAK,cAAc;IAC9E,MAAMsF,YAAY,GAAGF,QAAQ,IAAIpF,oBAAoB,KAAK,QAAQ;IAClE,MAAMuF,aAAa,GAAGH,QAAQ,KAAKpF,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;IAE5G,IAAImF,gBAAgB,EAAE;MAClB,MAAMK,gBAAgB,GAAGlF,WAAW,GAAG,CAAC;MAExC,IAAI+E,kBAAkB,EAAE;QAAA,IAAAI,qBAAA,EAAAC,sBAAA;QACpB,MAAMC,gBAAgB,IAAAF,qBAAA,GAAGvF,oBAAoB,CAACsF,gBAAgB,CAAC,cAAAC,qBAAA,wBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCG,UAAU,cAAAF,sBAAA,uBAAlDA,sBAAA,CAAoDG,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAACjD,EAAE,KAAK0B,WAAW,IAAIuB,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIJ,gBAAgB,EAAE;UAClBvF,qBAAqB,CAACmE,WAAW,EAAEW,UAAU,CAAC;QAClD;MACJ,CAAC,MAAM;QAAA,IAAAc,qBAAA,EAAAC,sBAAA;QACH,MAAMC,qBAAqB,IAAAF,qBAAA,GAAG/F,yBAAyB,CAACuF,gBAAgB,CAAC,cAAAQ,qBAAA,wBAAAC,sBAAA,GAA3CD,qBAAA,CAA6CJ,UAAU,cAAAK,sBAAA,uBAAvDA,sBAAA,CAAyDJ,IAAI,CACtFC,SAAc,IAAKA,SAAS,CAACjD,EAAE,KAAK0B,WAAW,IAAIuB,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;QAED,IAAIG,qBAAqB,EAAE;UACvB/F,0BAA0B,CAACoE,WAAW,EAAEW,UAAU,CAAC;QACvD;MACJ;IACJ,CAAC,MAAM,IAAIE,QAAQ,KAAKE,YAAY,IAAIC,aAAa,CAAC,EAAE;MAAA,IAAAY,sBAAA,EAAAC,sBAAA;MACpD,MAAMZ,gBAAgB,GAAGlF,WAAW,GAAG,CAAC;MACxC,MAAMqF,gBAAgB,IAAAQ,sBAAA,GAAGjG,oBAAoB,CAACsF,gBAAgB,CAAC,cAAAW,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCP,UAAU,cAAAQ,sBAAA,uBAAlDA,sBAAA,CAAoDP,IAAI,CAC5EC,SAAc,IAAKA,SAAS,CAACjD,EAAE,KAAK0B,WAAW,IAAIuB,SAAS,CAACC,IAAI,KAAK,KAC3E,CAAC;MAED,IAAIJ,gBAAgB,EAAE;QAClBvF,qBAAqB,CAACmE,WAAW,EAAEW,UAAU,CAAC;MAClD;IACJ,CAAC,MAAM;MACHvF,kBAAkB,CAAC4E,WAAW,EAAElD,KAAK,EAAE6D,UAAU,CAAC;IACtD;IAEAtF,mBAAmB,CAAC,IAAI,CAAC;IACzBgF,kBAAkB,CAACL,WAAW,EAAEW,UAAU,CAAC;EAC/C,CAAC;EAED,MAAMmB,oBAAoB,GAAI9B,WAAmB,IAAK;IAClD,IAAIjF,eAAe,EAAE;MACjB;IACJ;IACAO,iBAAiB,CAAC0E,WAAW,CAAC;IAC9B,IAAIlF,OAAO,EAAE;MACTA,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EAED,MAAMiH,mBAAmB,GAAGA,CAAC/B,WAAmB,EAAElD,KAAa,KAAK;IAChE,MAAM8D,gBAAgB,GAAG9E,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;IAEzH,IAAImF,gBAAgB,EAAE;MAClBrF,eAAe,CAACyE,WAAW,EAAElD,KAAK,CAAC;IACvC,CAAC,MAAM;MACHvB,eAAe,CAACyE,WAAW,EAAElD,KAAK,CAAC;IACvC;IAEAnC,sBAAsB,CAACC,KAAK,CAAC;EACjC,CAAC;EAED,MAAMoH,WAAW,GAAI1E,KAA2C,IAAK;IACjEA,KAAK,CAAC2E,cAAc,CAAC,CAAC;IAEtB,MAAMC,aAAa,GAAG5E,KAAK,CAAC4E,aAAa;IACzC,MAAMC,UAAU,GAAGD,aAAa,CAACE,OAAO,CAAC,YAAY,CAAC;IACtD,MAAMC,UAAU,GAAGH,aAAa,CAACE,OAAO,CAAC,WAAW,CAAC;IAErD,IAAIC,UAAU,EAAE;MACZ,MAAMC,YAAY,GAAGD,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC;MACtD,IAAID,YAAY,EAAE;QACdE,aAAa,CAACH,UAAU,CAAC;MAC7B,CAAC,MAAM;QACHG,aAAa,CAACH,UAAU,CAAC;MAC7B;IACJ,CAAC,MAAM;MACHG,aAAa,CAACL,UAAU,CAAC;IAC7B;EACJ,CAAC;EAED,MAAMK,aAAa,GAAI9C,OAAe,IAAK;IACvC,IAAIzD,YAAY,EAAE;MACd,MAAM6C,SAAS,GAAGjC,YAAY,CAACZ,YAAY,CAAC;MAC5C,IAAI6C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAE/B,OAAO,EAAE;QACpB,MAAMkC,MAAM,GAAIH,SAAS,CAAC/B,OAAO,CAASkC,MAAM;QAChDA,MAAM,CAACG,SAAS,CAACqD,UAAU,CAAC/C,OAAO,CAAC;MACxC;IACJ;EACJ,CAAC;EAED,MAAMgD,aAAa,GAAI5F,KAAa,IAAK;IACrC,IAAIX,mBAAmB,KAAKW,KAAK,EAAE;MAC/BV,sBAAsB,CAAC,IAAI,CAAC;IAChC,CAAC,MAAM;MACHA,sBAAsB,CAACU,KAAK,CAAC;IACjC;EACJ,CAAC;EAED,MAAM,CAAC6F,cAAc,EAAEC,iBAAiB,CAAC,GAAGxJ,QAAQ,CAAU,KAAK,CAAC;EAEpEC,SAAS,CAAC,MAAM;IACZ,MAAMwJ,GAAG,GAAG7E,QAAQ,CAAC8E,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK;IACtDH,iBAAiB,CAACC,GAAG,CAACG,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,MAAM,GAAGzJ,OAAO,CAClB,OAAY;IACR0J,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAER,cAAc,GAAG,KAAK,GAAY,KAAc;IAC3DS,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,OAAO,EAAEpH,mBAAmB,KAAK,IAAI;IAErC;IACAqH,KAAK,EAAE,GAAG;IAAE;IACZC,UAAU,EAAE,GAAG;IAAE;IACjBC,WAAW,EAAE,CAAC;IAAE;;IAEhB;IACAC,SAAS,EAAE;MACPC,kBAAkB,EAAE,IAAI;MAAE;MAC1BC,WAAW,EAAE,KAAK;MAAE;MACpBC,SAAS,EAAE;IACf,CAAC;IAED;IACAC,SAAS,EAAE,KAAK;IAAE;IAClBC,oBAAoB,EAAE,KAAc;IAAE;;IAEtC;IACAC,eAAe,EAAE,KAAK;IAAE;;IAExB;IACAC,cAAc,EAAE,CAAC,SAAS,CAAC;IAAE;;IAE7B;IACAC,gBAAgB,EAAE;MACd,iBAAiB,EAAE,aAAa;MAAE;MAClC,iBAAiB,EAAE,OAAO,CAAC;IAC/B,CAAC;IAEDC,OAAO,EAAE,CACL,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EACnE,MAAM,EAAE,UAAU,EAAE,MAAM,EAC1B;MACIC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE,CACF,QAAQ,EACR,OAAO,EAAE,OAAO,EAAE,OAAO,EACzB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAC5B,IAAI,EAAE,QAAQ,EAAE,YAAY,EAC5B,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,EAC9D,SAAS,EAAE,QAAQ,EAAE,WAAW;IAExC,CAAC,CACJ;IAEDC,SAAS,EAAExG,QAAQ,CAAC8E,IAAI;IACxB2B,MAAM,EAAE,MAAM;IACdC,cAAc,EAAE,KAAK;IAErB;IACAtF,SAAS,EAAE;MACPuF,IAAI,EAAE;IACV,CAAC;IAEDC,IAAI,EAAE;MACFC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE,IAAI;MAC1BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE;IACnB,CAAC;IAEDC,MAAM,EAAE;MACJV,MAAM,EAAE;IACZ,CAAC;IAED;IACAW,MAAM,EAAE;MACJ;MACAC,OAAO,EAAErD,WAAW;MAEpB;MACAsD,SAAS,EAAGC,KAAU,IAAK;QACvB;QACAxG,UAAU,CAAC,MAAM;UACb,MAAME,MAAM,GAAGsG,KAAK,CAACtG,MAAM;UAC3B,IAAIA,MAAM,EAAE;YACR;YACA,MAAME,KAAK,GAAGoG,KAAK,CAACnG,SAAS,CAACC,WAAW,CAAC,CAAC;YAC3CF,KAAK,CAACG,kBAAkB,CAACL,MAAM,CAAC;YAChCE,KAAK,CAACI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YACvBgG,KAAK,CAACnG,SAAS,CAACI,WAAW,CAACL,KAAK,CAAC;UACtC;QACJ,CAAC,EAAE,GAAG,CAAC;MACX,CAAC;MAED;MACAD,KAAK,EAAGqG,KAAU,IAAK;QACnB;QACAxG,UAAU,CAAC,MAAM;UACb,MAAMK,SAAS,GAAGmG,KAAK,CAACnG,SAAS;UACjC,IAAIA,SAAS,IAAI,CAACA,SAAS,CAACoG,WAAW,CAAC,CAAC,EAAE;YACvC;YACA,MAAMrG,KAAK,GAAGC,SAAS,CAACC,WAAW,CAAC,CAAC;YACrCF,KAAK,CAACG,kBAAkB,CAACiG,KAAK,CAACtG,MAAM,CAAC;YACtCE,KAAK,CAACI,QAAQ,CAAC,KAAK,CAAC;YACrBH,SAAS,CAACI,WAAW,CAACL,KAAK,CAAC;UAChC;QACJ,CAAC,EAAE,CAAC,CAAC;MACT,CAAC;MAED;MACAsG,OAAO,EAAEA,CAACnI,KAAoB,EAAEiI,KAAU,KAAK;QAC3C,IAAIjI,KAAK,CAACoI,GAAG,KAAK,OAAO,IAAI,CAACpI,KAAK,CAACqI,QAAQ,EAAE;UAC1C;UACA;UACA,OAAO,IAAI;QACf,CAAC,MAAM,IAAIrI,KAAK,CAACoI,GAAG,KAAK,OAAO,IAAIpI,KAAK,CAACqI,QAAQ,EAAE;UAChD;UACArI,KAAK,CAAC2E,cAAc,CAAC,CAAC;UACtB,MAAM2D,EAAE,GAAGL,KAAK,CAACM,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC;UAC3CP,KAAK,CAACnG,SAAS,CAAC2G,UAAU,CAACH,EAAE,CAAC;;UAE9B;UACA,MAAMzG,KAAK,GAAGoG,KAAK,CAACnG,SAAS,CAACC,WAAW,CAAC,CAAC;UAC3CF,KAAK,CAAC6G,aAAa,CAACJ,EAAE,CAAC;UACvBzG,KAAK,CAACI,QAAQ,CAAC,IAAI,CAAC;UACpBgG,KAAK,CAACnG,SAAS,CAACI,WAAW,CAACL,KAAK,CAAC;UAElC,OAAO,KAAK;QAChB;QACA,OAAO,IAAI;MACf,CAAC;MAED;MACA8G,YAAY,EAAGV,KAAU,IAAK;QAC1B;QACAxG,UAAU,CAAC,MAAM;UACbwG,KAAK,CAACnG,SAAS,CAACF,KAAK,CAAC,CAAC;QAC3B,CAAC,EAAE,EAAE,CAAC;MACV,CAAC;MAED;MACAgH,MAAM,EAAEA,CAACxG,OAAe,EAAE6F,KAAU,KAAK;QACrC;MAAA;IAER,CAAC;IAEDY,QAAQ,EAAE;MACNC,IAAI,EAAE;QACF7B,IAAI,EAAE;UACF,qBAAqB,EAAE,SAAS;UAChC,oBAAoB,EAAE,QAAQ;UAC9B,2BAA2B,EAAE,eAAe;UAC5C,uBAAuB,EAAE,WAAW;UACpC,qBAAqB,EAAE,SAAS;UAChC,4BAA4B,EAAE;QAClC;MACJ;IACJ;EACJ,CAAC,CAAC,EACF,CAAC5B,cAAc,EAAExG,mBAAmB,CACxC,CAAC;;EAED;EACA,MAAMyE,gBAAgB,GAAG9E,YAAY,KAAKN,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAMoF,QAAQ,GAAG/E,YAAY,IAAIN,gBAAgB,KAAK,MAAM;EAC5D,MAAMsF,kBAAkB,GAAGD,QAAQ,IAAIpF,oBAAoB,KAAK,cAAc;EAC9E,MAAMsF,YAAY,GAAGF,QAAQ,IAAIpF,oBAAoB,KAAK,QAAQ;EAClE,MAAMuF,aAAa,GAAGH,QAAQ,KAAKpF,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAS,CAAC;EAC5G,MAAMwF,gBAAgB,GAAGlF,WAAW,GAAG,CAAC;EAExC,IAAIsK,kBAAyB,GAAG,EAAE;EAElC,IAAIzF,gBAAgB,IAAI,CAACE,kBAAkB,EAAE;IACzCuF,kBAAkB,GAAGrK,8BAA8B,CAACiF,gBAAgB,EAAE,KAAK,CAAC;EAChF,CAAC,MAAM,IAAIJ,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAC,EAAE;IAAA,IAAAwF,sBAAA;IAC1E,KAAAA,sBAAA,GAAI3K,oBAAoB,CAACsF,gBAAgB,CAAC,cAAAqF,sBAAA,eAAtCA,sBAAA,CAAwCjF,UAAU,EAAE;MACpDgF,kBAAkB,GAAG1K,oBAAoB,CAACsF,gBAAgB,CAAC,CAACI,UAAU,CAACkF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChF,IAAI,KAAK,KAAK,CAAC;IACxG,CAAC,MAAM;MACH6E,kBAAkB,GAAG,EAAE;IAC3B;EACJ,CAAC,MAAM;IACHA,kBAAkB,GAAGlL,aAAa;EACtC;EAEA,oBACIhB,OAAA,CAAAE,SAAA;IAAAoM,QAAA,EACKJ,kBAAkB,CAACK,GAAG,CAAEC,IAAS,IAAK;MACnC,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAI9J,KAAK,GAAG,EAAE;MACd,IAAIwB,EAAE,GAAG,EAAE;MAEX,IAAKsC,gBAAgB,IAAI,CAACE,kBAAkB,IAAMD,QAAQ,KAAKE,YAAY,IAAIC,aAAa,IAAIF,kBAAkB,CAAE,EAAE;QAClH8F,OAAO,GAAGD,IAAI,CAACE,WAAW,IAAI,EAAE;QAChC/J,KAAK,GAAG6J,IAAI,CAACrI,EAAE;QACfA,EAAE,GAAGqI,IAAI,CAACrI,EAAE;MAChB,CAAC,MAAM;QAAA,IAAAwI,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,YAAA;QACHL,OAAO,GAAG,EAAAE,UAAA,GAAAH,IAAI,CAACO,IAAI,cAAAJ,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBI,IAAI,KAAI,EAAE;QACpCrK,KAAK,IAAAkK,WAAA,GAAGL,IAAI,CAACO,IAAI,cAAAF,WAAA,wBAAAC,YAAA,GAATD,WAAA,CAAY,CAAC,CAAC,cAAAC,YAAA,uBAAdA,YAAA,CAAgB3I,EAAE;QAC1BA,EAAE,GAAGqI,IAAI,CAACrI,EAAE;MAChB;MAEA,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;MAEpB,MAAMK,mBAAmB,GAAGvB,eAAe,CAACkB,EAAE,CAAC;MAC/C,MAAM8I,gBAAgB,GAAGvK,YAAY,CAACyB,EAAE,CAAC;MAEzC,oBACInE,OAAA,CAACV,GAAG;QAEAuB,GAAG,EAAE2D,mBAAoB;QACzB0I,EAAE,EAAE;UACAC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,UAAU;UACpB,0BAA0B,EAAE;YACxBF,OAAO,EAAE;UACb,CAAC;UACD,iBAAiB,EAAE;YACfG,UAAU,EAAE;UAChB,CAAC;UACD,oBAAoB,EAAE;YAClBA,UAAU,EAAE;UAChB,CAAC;UACD,8BAA8B,EAAE;YAC5BC,QAAQ,EAAE;UACd,CAAC;UACD,wBAAwB,EAAE;YACtBC,KAAK,EAAEnM,gBAAgB,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtDoM,UAAU,EAAE,UAAU;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,kBAAkB,EAAE;YAChBC,SAAS,EAAEtM,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAG,IAAI;YACzIsM,SAAS,EACLvM,gBAAgB,KAAK,QAAQ,IAC5BA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAEhE,iBAAiB,GAEfD,gBAAgB,KAAK,cAAc,IAClCA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAe,GAEtE,gCAAgC,GAChC,IAAI;YACduM,QAAQ,EAAExM,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,QAAQ,GAAG;UAC/H,CAAC;UACD,kBAAkB,EAAE;YAChBiM,QAAQ,EAAElM,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAG,IAAI;YACxIqM,SAAS,EAAEtM,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAG;UACzI,CAAC;UACD,qBAAqB,EAAE;YACnB6L,OAAO,EAAE,iBAAiB;YAC1BW,cAAc,EAAE,mBAAmB;YACnCC,MAAM,EAAE1M,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAG,IAAI;YACtIsM,SAAS,EAAEvM,gBAAgB,KAAK,QAAQ,IAAKA,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,QAAS,GAAG,iBAAiB,GAAG;UACzI,CAAC;UACD;UACA,qBAAqB,EAAE;YACnB+L,QAAQ,EAAE,kBAAkB;YAC5B/C,MAAM,EAAE,mBAAmB;YAC3B0D,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf,CAAC;UACD,oCAAoC,EAAE;YAClCb,QAAQ,EAAE,qBAAqB;YAC/BW,GAAG,EAAE,iBAAiB;YACtBC,IAAI,EAAE,iBAAiB;YACvBC,SAAS,EAAE,iBAAiB;YAC5BC,QAAQ,EAAE,kBAAkB;YAC5BC,UAAU,EAAE,kBAAkB;YAC9BC,MAAM,EAAE,2BAA2B;YACnCC,YAAY,EAAE,gBAAgB;YAC9BC,SAAS,EAAE;UACf,CAAC;UACD,qBAAqB,EAAE;YACnBlB,QAAQ,EAAE,kBAAkB;YAC5B/C,MAAM,EAAE,mBAAmB;YAC3B0D,GAAG,EAAE,gBAAgB;YACrBC,IAAI,EAAE,gBAAgB;YACtBC,SAAS,EAAE;UACf;QACJ,CAAE;QACFM,SAAS,EAAC,WAAW;QAAAlC,QAAA,EAEnBjL,gBAAgB,KAAK,cAAc,IAAIA,gBAAgB,KAAK,SAAS,IAAIA,gBAAgB,KAAK,SAAS,IAAMC,oBAAoB,KAAK,cAAc,IAAIA,oBAAoB,KAAK,SAAS,IAAIA,oBAAoB,KAAK,SAAU,gBAC/NtB,OAAA,CAACT,OAAO;UACJkP,KAAK,eACDzO,OAAA,CAAAE,SAAA;YAAAoM,QAAA,gBACItM,OAAA,CAACR,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAMhH,oBAAoB,CAAC6E,IAAI,CAACrI,EAAE,CAAE;cAC7CyK,QAAQ,EAAEhO,eAAgB;cAC1B6N,KAAK,EAAE7N,eAAe,GAAGG,SAAS,CAAC,+CAA+C,CAAC,GAAGA,SAAS,CAAC,eAAe,CAAE;cACjHmM,EAAE,EAAE;gBACA,SAAS,EAAE;kBACP2B,eAAe,EAAE;gBACrB,CAAC;gBACDC,GAAG,EAAE;kBACDf,MAAM,EAAE,MAAM;kBACdgB,IAAI,EAAE;oBACFC,IAAI,EAAE;kBACV;gBACJ;cACJ,CAAE;cAAA1C,QAAA,eAEFtM,OAAA;gBACIiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAEvP;gBAAS,CAAE;gBAC9CwP,KAAK,EAAE;kBACHC,OAAO,EAAExO,eAAe,GAAG,GAAG,GAAG,CAAC;kBAClCmN,MAAM,EAAE;gBACZ;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACbxP,OAAA,CAACR,UAAU;cAACkP,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAM/G,mBAAmB,CAAC4E,IAAI,CAACrI,EAAE,EAAExB,KAAK,CAAE;cACxEuK,EAAE,EAAE;gBACA,SAAS,EAAE;kBACP2B,eAAe,EAAE;gBACrB,CAAC;gBACDC,GAAG,EAAE;kBACDC,IAAI,EAAE;oBACFC,IAAI,EAAE;kBACV;gBACJ;cACJ,CAAE;cAAA1C,QAAA,eAEFtM,OAAA;gBAAMiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAEtP;gBAAW,CAAE;gBAClDuP,KAAK,EAAE;kBACHpB,MAAM,EAAE;gBACZ;cAAE;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA,eACf,CACL;UACDC,SAAS,EAAC,KAAK;UACfC,SAAS,EAAE;YACPC,OAAO,EAAE;cACLzC,EAAE,EAAE;gBACA2B,eAAe,EAAE,OAAO;gBACxBe,KAAK,EAAE,OAAO;gBACdtB,YAAY,EAAE,KAAK;gBACnBuB,OAAO,EAAE,SAAS;gBAClBxB,MAAM,EAAE,gCAAgC;gBACxCyB,YAAY,EAAE;cAClB;YACJ;UACJ,CAAE;UACFC,WAAW,EAAE;YACTC,SAAS,EAAE,CACP;cACI9F,IAAI,EAAE,iBAAiB;cACvB+F,OAAO,EAAE;gBAAEC,QAAQ,EAAE;cAAW;YACpC,CAAC,EACD;cACIhG,IAAI,EAAE,MAAM;cACZ+F,OAAO,EAAE;gBAAEE,OAAO,EAAE;cAAK;YAC7B,CAAC;UAET,CAAE;UAAA7D,QAAA,eAEFtM,OAAA;YAAKmP,KAAK,EAAE;cAAE3B,KAAK,EAAE,MAAM;cAAEH,QAAQ,EAAE;YAAW,CAAE;YAAAf,QAAA,gBAChDtM,OAAA,CAACP,WAAW;cACRoB,GAAG,EAAEoM,gBAAiB;cACtBmD,KAAK,EAAE3D,OAAQ;cACf3D,MAAM,EAAEA,MAAO;cACfuH,QAAQ,EAAG7J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAE7D,KAAK,EAAEwB,EAAE,CAAE;cAC9DmM,OAAO,EAAEA,CAAA,KAAMvO,eAAe,CAACoC,EAAE;YAAE;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACFxP,OAAA,CAACR,UAAU;cACPkP,IAAI,EAAC,OAAO;cACZC,OAAO,EAAG4B,CAAC,IAAK;gBACZA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnBjI,aAAa,CAACpE,EAAE,CAAC;cACrB,CAAE;cACF+I,EAAE,EAAE;gBACAG,QAAQ,EAAE,UAAU;gBACpBoD,MAAM,EAAE,KAAK;gBACbC,KAAK,EAAE,KAAK;gBACZlD,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE,MAAM;gBACdc,eAAe,EAAE,0BAA0B;gBAC3CvE,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE;kBACPuE,eAAe,EAAE;gBACrB,CAAC;gBACD,OAAO,EAAE;kBACLrB,KAAK,EAAE,MAAM;kBACbO,MAAM,EAAE;gBACZ;cACJ,CAAE;cACFU,KAAK,EAAE1N,SAAS,CAAC,gBAAgB,CAAE;cAAAuL,QAAA,eAEnCtM,OAAA;gBACIiP,uBAAuB,EAAE;kBAAEC,MAAM,EAAErP;gBAAS,CAAE;gBAC9CsP,KAAK,EAAE;kBAAEpB,MAAM,EAAE,MAAM;kBAAEP,KAAK,EAAE;gBAAO;cAAE;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,gBAEVxP,OAAA;UAAKmP,KAAK,EAAE;YAAE3B,KAAK,EAAE,MAAM;YAAEH,QAAQ,EAAE;UAAW,CAAE;UAAAf,QAAA,gBAChDtM,OAAA,CAACP,WAAW;YACRoB,GAAG,EAAEoM,gBAAiB;YACtBmD,KAAK,EAAE3D,OAAQ;YACf3D,MAAM,EAAEA,MAAO;YACfuH,QAAQ,EAAG7J,UAAU,IAAKD,YAAY,CAACC,UAAU,EAAE7D,KAAK,EAAEwB,EAAE,CAAE;YAC9DmM,OAAO,EAAEA,CAAA,KAAMvO,eAAe,CAACoC,EAAE;UAAE;YAAAkL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFxP,OAAA,CAACR,UAAU;YACPkP,IAAI,EAAC,OAAO;YACZC,OAAO,EAAG4B,CAAC,IAAK;cACZA,CAAC,CAACC,eAAe,CAAC,CAAC;cACnBjI,aAAa,CAACpE,EAAE,CAAC;YACrB,CAAE;YACF+I,EAAE,EAAE;cACAG,QAAQ,EAAE,UAAU;cACpBoD,MAAM,EAAE,KAAK;cACbC,KAAK,EAAE,KAAK;cACZlD,KAAK,EAAE,MAAM;cACbO,MAAM,EAAE,MAAM;cACdc,eAAe,EAAE,0BAA0B;cAC3CvE,MAAM,EAAE,IAAI;cACZ,SAAS,EAAE;gBACPuE,eAAe,EAAE;cACrB,CAAC;cACD,OAAO,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbO,MAAM,EAAE;cACZ;YACJ,CAAE;YACFU,KAAK,EAAE1N,SAAS,CAAC,gBAAgB,CAAE;YAAAuL,QAAA,eAEnCtM,OAAA;cACIiP,uBAAuB,EAAE;gBAAEC,MAAM,EAAErP;cAAS,CAAE;cAC9CsP,KAAK,EAAE;gBAAEpB,MAAM,EAAE,MAAM;gBAAEP,KAAK,EAAE;cAAO;YAAE;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACR,GAtOIrL,EAAE;QAAAkL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuON,CAAC;IAEd,CAAC;EAAC,gBACJ,CAAC;AAEX,CAAC;EAAA,QAvrB4B1P,cAAc,EAgBnCJ,cAAc;AAAA,EAwqB1B,CAAC;EAAA,QAxrBgCI,cAAc,EAgBnCJ,cAAc;AAAA,EAwqBzB;AAACiR,GAAA,GA1rBIxQ,UAAqC;AA4rB3C,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAsQ,GAAA;AAAAC,YAAA,CAAAvQ,EAAA;AAAAuQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}